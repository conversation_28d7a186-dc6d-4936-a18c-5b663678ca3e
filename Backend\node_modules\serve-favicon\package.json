{"name": "serve-favicon", "description": "favicon serving middleware with caching", "version": "2.5.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["express", "favicon", "middleware"], "repository": "expressjs/serve-favicon", "dependencies": {"etag": "~1.8.1", "fresh": "~0.5.2", "ms": "~2.1.3", "parseurl": "~1.3.2", "safe-buffer": "~5.2.1"}, "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "mocha": "10.8.2", "nyc": "^15.1.0", "supertest": "7.1.1", "temp-path": "1.0.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}